import { motion } from "framer-motion";
import { useInView } from "framer-motion";
import { useRef, useState, useEffect } from "react";

export default function TestimonialsSection() {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });
  const [currentTestimonial, setCurrentTestimonial] = useState(0);

  const testimonials = [
    {
      text: "<PERSON><PERSON><PERSON><PERSON> transformed our channel's visual identity completely. Our click-through rates increased by 45% within the first month. Their team understands what makes viewers click!",
      name: "<PERSON>",
      avatar: "👨‍💻",
    },
    {
      text: "Working with <PERSON><PERSON><PERSON><PERSON> was a game-changer. They don't just create beautiful designs – they create strategic visuals that convert. Our subscriber growth doubled!",
      name: "<PERSON>",
      avatar: "👩‍🎨",
    },
    {
      text: "Professional, fast, and incredibly creative. B<PERSON><PERSON><PERSON>'s thumbnails helped us reach 1M views faster than we ever imagined. Highly recommend their services!",
      name: "<PERSON>",
      avatar: "👨‍💼",
    },
  ];

  // Auto-rotate testimonials
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTestimonial((prev) => (prev + 1) % testimonials.length);
    }, 5000);
    return () => clearInterval(interval);
  }, [testimonials.length]);

  return (
    <section
      id='testimonials'
      className='py-20 px-6 lg:px-12 bg-white'
      ref={ref}
    >
      <div className='max-w-7xl mx-auto'>
        <div className='grid grid-cols-1 lg:grid-cols-2 gap-16 items-center'>
          {/* Left Side - Section Info */}
          <motion.div
            className='space-y-8'
            initial={{ opacity: 0, x: -50 }}
            animate={isInView ? { opacity: 1, x: 0 } : { opacity: 0, x: -50 }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            {/* Stats */}
            <div className='space-y-4'>
              <div className='text-5xl font-bold text-[#FF4500]'>98%</div>
              <div className='text-2xl font-bold text-gray-900'>
                Customer Satisfaction
              </div>
            </div>

            {/* Heading */}
            <div className='space-y-4'>
              <h2 className='text-3xl md:text-4xl font-bold text-gray-900'>
                Testimonials from our customers.
              </h2>
              <p className='text-gray-600 leading-relaxed'>
                Read our clients' success stories and discover how our services
                helped elevate their brands.
              </p>
            </div>
          </motion.div>

          {/* Right Side - Testimonial Carousel */}
          <motion.div
            className='relative'
            initial={{ opacity: 0, x: 50 }}
            animate={isInView ? { opacity: 1, x: 0 } : { opacity: 0, x: 50 }}
            transition={{ duration: 0.8, delay: 0.4 }}
          >
            <div className='bg-white rounded-3xl p-8 shadow-lg border border-gray-100 relative'>
              {/* Navigation Arrows */}
              <div className='absolute top-1/2 -translate-y-1/2 -left-6 z-10'>
                <button
                  onClick={() =>
                    setCurrentTestimonial(
                      (prev) =>
                        (prev - 1 + testimonials.length) % testimonials.length
                    )
                  }
                  className='w-12 h-12 bg-white rounded-full shadow-lg flex items-center justify-center hover:bg-[#FF4500] hover:text-white transition-all duration-300'
                >
                  <i className='fas fa-chevron-left' />
                </button>
              </div>

              <div className='absolute top-1/2 -translate-y-1/2 -right-6 z-10'>
                <button
                  onClick={() =>
                    setCurrentTestimonial(
                      (prev) => (prev + 1) % testimonials.length
                    )
                  }
                  className='w-12 h-12 bg-white rounded-full shadow-lg flex items-center justify-center hover:bg-[#FF4500] hover:text-white transition-all duration-300'
                >
                  <i className='fas fa-chevron-right' />
                </button>
              </div>

              {/* Stars */}
              <div className='flex text-[#FF4500] text-xl mb-6'>
                {[...Array(5)].map((_, i) => (
                  <i key={i} className='fas fa-star' />
                ))}
              </div>

              {/* Testimonial Content */}
              <motion.div
                key={currentTestimonial}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.5 }}
                className='space-y-6'
              >
                <blockquote className='text-gray-700 text-lg leading-relaxed'>
                  {testimonials[currentTestimonial].text}
                </blockquote>

                {/* Author */}
                <div className='flex items-center space-x-4'>
                  <div className='w-12 h-12 bg-green-100 rounded-full flex items-center justify-center text-2xl'>
                    {testimonials[currentTestimonial].avatar}
                  </div>
                  <div>
                    <div className='font-bold text-gray-900'>
                      {testimonials[currentTestimonial].name}
                    </div>
                  </div>
                </div>
              </motion.div>

              {/* Dots Indicator */}
              <div className='flex justify-center space-x-2 mt-8'>
                {testimonials.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => setCurrentTestimonial(index)}
                    className={`w-3 h-3 rounded-full transition-all duration-300 ${
                      currentTestimonial === index
                        ? "bg-[#FF4500] scale-125"
                        : "bg-gray-300 hover:bg-gray-400"
                    }`}
                  />
                ))}
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
