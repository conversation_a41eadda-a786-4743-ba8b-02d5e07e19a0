import { motion } from "framer-motion";

export default function HeroSection() {
  return (
    <motion.section
      id='hero'
      className='min-h-screen flex items-center justify-center px-6 lg:px-12 py-32 relative overflow-hidden'
      style={{
        background:
          'linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%), url("data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23FF4500" fill-opacity="0.03"%3E%3Cpath d="M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")',
      }}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 1 }}
    >
      <div className='max-w-7xl mx-auto text-center z-10 relative w-full'>
        <div className='space-y-8'>
          {/* Main Title */}
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            <h1 className='text-5xl md:text-7xl lg:text-8xl font-bold text-gray-900 tracking-[0.1em] leading-none'>
              YOUTUBE <span className='text-[#FF4500]'>THUMBNAILS</span>
            </h1>
          </motion.div>

          {/* Subheading */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
          >
            <p className='text-xl md:text-2xl lg:text-3xl font-normal text-gray-600 max-w-4xl mx-auto leading-relaxed'>
              Where Vision Meets Performance
            </p>
          </motion.div>

          {/* CTA Buttons */}
          <motion.div
            className='flex flex-col sm:flex-row gap-6 items-center justify-center pt-12'
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 1.0 }}
          >
            <motion.a
              href='#contact'
              className='px-10 py-4 bg-[#FF4500] text-white font-semibold text-lg rounded-full shadow-lg hover:shadow-xl transition-all duration-300'
              whileHover={{
                scale: 1.05,
                boxShadow: "0 20px 40px rgba(255, 69, 0, 0.3)",
              }}
              whileTap={{ scale: 0.95 }}
            >
              Let's Make Thumbnails That Convert
            </motion.a>

            <motion.a
              href='#portfolio'
              className='px-10 py-4 bg-transparent text-[#FF4500] font-semibold text-lg rounded-full border-2 border-[#FF4500] hover:bg-[#FF4500] hover:text-white transition-all duration-300'
              whileHover={{
                scale: 1.05,
              }}
              whileTap={{ scale: 0.95 }}
            >
              See My Work
            </motion.a>
          </motion.div>
        </div>
      </div>
    </motion.section>
  );
}
