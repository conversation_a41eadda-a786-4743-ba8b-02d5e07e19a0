import { motion } from "framer-motion";

export default function HeroSection() {
  return (
    <motion.section
      id='hero'
      className='min-h-screen flex items-center justify-center px-6 lg:px-12 py-32 relative overflow-hidden'
      style={{
        background:
          'linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%), url("data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23FF4500" fill-opacity="0.03"%3E%3Cpath d="M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")',
      }}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 1 }}
    >
      {/* Floating Thumbnail Elements */}
      <motion.div
        className='absolute top-20 left-10 w-16 h-12 bg-gradient-to-r from-red-500 to-red-600 rounded-lg shadow-lg opacity-20'
        animate={{
          y: [0, -20, 0],
          rotate: [0, 5, 0],
        }}
        transition={{ duration: 4, repeat: Infinity }}
      />
      <motion.div
        className='absolute top-40 right-20 w-20 h-14 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg shadow-lg opacity-20'
        animate={{
          y: [0, 20, 0],
          rotate: [0, -5, 0],
        }}
        transition={{ duration: 5, repeat: Infinity, delay: 1 }}
      />
      <motion.div
        className='absolute bottom-40 left-20 w-18 h-13 bg-gradient-to-r from-green-500 to-green-600 rounded-lg shadow-lg opacity-20'
        animate={{
          y: [0, -15, 0],
          rotate: [0, 3, 0],
        }}
        transition={{ duration: 6, repeat: Infinity, delay: 2 }}
      />

      {/* Play Button Icons */}
      <motion.div
        className='absolute top-32 right-40 text-[#FF4500] opacity-10'
        animate={{
          scale: [1, 1.2, 1],
          rotate: [0, 360],
        }}
        transition={{ duration: 8, repeat: Infinity }}
      >
        <i className='uil uil-play text-6xl' />
      </motion.div>
      <motion.div
        className='absolute bottom-32 left-40 text-blue-500 opacity-10'
        animate={{
          scale: [1, 1.1, 1],
          rotate: [0, -360],
        }}
        transition={{ duration: 10, repeat: Infinity, delay: 3 }}
      >
        <i className='uil uil-video text-5xl' />
      </motion.div>
      <div className='max-w-7xl mx-auto text-center z-10 relative w-full'>
        <div className='space-y-8'>
          {/* Main Title */}
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            <h1 className='text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 tracking-[0.1em] leading-none'>
              THUMBNAILS THAT <span className='text-[#FF4500]'>CLICK</span>
            </h1>
          </motion.div>

          {/* Subheading */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
          >
            <p className='text-lg md:text-xl lg:text-2xl font-normal text-gray-600 max-w-4xl mx-auto leading-relaxed'>
              Where Vision Meets Performance
            </p>
          </motion.div>

          {/* CTA Buttons */}
          <motion.div
            className='flex flex-col sm:flex-row gap-6 items-center justify-center pt-12'
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 1.0 }}
          >
            <motion.a
              href='#contact'
              className='px-8 py-3 bg-[#FF4500] text-white font-semibold text-base rounded-full shadow-lg hover:shadow-xl transition-all duration-300'
              whileHover={{
                scale: 1.05,
                boxShadow: "0 20px 40px rgba(255, 69, 0, 0.3)",
              }}
              whileTap={{ scale: 0.95 }}
            >
              Let's Make Thumbnails That Convert
            </motion.a>

            <motion.a
              href='#portfolio'
              className='px-8 py-3 bg-transparent text-[#FF4500] font-semibold text-base rounded-full border-2 border-[#FF4500] hover:bg-[#FF4500] hover:text-white transition-all duration-300'
              whileHover={{
                scale: 1.05,
              }}
              whileTap={{ scale: 0.95 }}
            >
              See My Work
            </motion.a>
          </motion.div>
        </div>
      </div>
    </motion.section>
  );
}
