import { useKeenSlider } from "keen-slider/react";
import { motion, AnimatePresence } from "framer-motion";
import { useRef, useState } from "react";
import { ChevronLeft, ChevronRight, ZoomIn, ExternalLink } from "lucide-react";

export default function PortfolioSection() {
  const ref = useRef(null);
  const [currentSlide, setCurrentSlide] = useState(0);
  const [loaded, setLoaded] = useState(false);
  const [selectedImage, setSelectedImage] = useState<string | null>(null);

  const [sliderRef, instanceRef] = useKeenSlider({
    loop: true,
    slides: {
      perView: 3,
      spacing: 24,
    },
    breakpoints: {
      "(max-width: 640px)": {
        slides: { perView: 1, spacing: 16 },
      },
      "(min-width: 641px) and (max-width: 1024px)": {
        slides: { perView: 2, spacing: 20 },
      },
    },
    slideChanged(slider) {
      setCurrentSlide(slider.track.details.rel);
    },
    created() {
      setLoaded(true);
    },
  });

  // Get all thumbnail files dynamically (removed thumbnails 5, 22, 23, 26)
  const thumbnails = [
    "/Bilal Thumbnails/1.png",
    "/Bilal Thumbnails/2.png",
    "/Bilal Thumbnails/3 (2).png",
    "/Bilal Thumbnails/4 (2).png",
    "/Bilal Thumbnails/6 (2).png",
    "/Bilal Thumbnails/7.png",
    "/Bilal Thumbnails/8.png",
    "/Bilal Thumbnails/9.png",
    "/Bilal Thumbnails/10.png",
    "/Bilal Thumbnails/11.png",
    "/Bilal Thumbnails/12.png",
    "/Bilal Thumbnails/13.png",
    "/Bilal Thumbnails/14.png",
    "/Bilal Thumbnails/15.png",
    "/Bilal Thumbnails/16.png",
    "/Bilal Thumbnails/17.png",
    "/Bilal Thumbnails/18.png",
    "/Bilal Thumbnails/19.png",
    "/Bilal Thumbnails/20.png",
    "/Bilal Thumbnails/21.png",
    "/Bilal Thumbnails/24.png",
    "/Bilal Thumbnails/25.png",
  ];

  return (
    <section
      id='portfolio'
      className='py-16 md:py-24 lg:py-32 px-6 lg:px-12 bg-gradient-to-br from-gray-50 via-white to-gray-50'
      ref={ref}
    >
      <div className='max-w-7xl mx-auto'>
        {/* Header */}
        <motion.div
          className='text-center space-y-8 mb-20'
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
        >
          <motion.div
            className='inline-block px-6 py-3 bg-gradient-to-r from-[#FF4500] to-[#FF6B35] text-white rounded-full text-sm font-semibold mb-6'
            initial={{ scale: 0 }}
            whileInView={{ scale: 1 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            ✨ Portfolio Showcase
          </motion.div>
          <h2 className='text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 leading-tight'>
            <span className='text-gradient'>Thumbnail Masterpieces</span>
          </h2>
          <p className='text-lg md:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed'>
            Discover my collection of eye-catching thumbnails designed to
            maximize clicks and engagement. Each piece tells a story and drives
            results.
          </p>
        </motion.div>

        {/* Enhanced Slider Container */}
        <div className='relative'>
          {/* Navigation Arrows */}
          {loaded && instanceRef.current && (
            <>
              <button
                onClick={(e: any) => {
                  e.stopPropagation();
                  instanceRef.current?.prev();
                }}
                className='absolute left-4 top-1/2 -translate-y-1/2 z-10 w-12 h-12 bg-white/90 backdrop-blur-sm rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center group hover:scale-110'
              >
                <ChevronLeft className='w-6 h-6 text-gray-700 group-hover:text-[#FF4500] transition-colors' />
              </button>
              <button
                onClick={(e: any) => {
                  e.stopPropagation();
                  instanceRef.current?.next();
                }}
                className='absolute right-4 top-1/2 -translate-y-1/2 z-10 w-12 h-12 bg-white/90 backdrop-blur-sm rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center group hover:scale-110'
              >
                <ChevronRight className='w-6 h-6 text-gray-700 group-hover:text-[#FF4500] transition-colors' />
              </button>
            </>
          )}

          {/* Slider */}
          <div ref={sliderRef} className='keen-slider'>
            {thumbnails.map((src, index) => (
              <div key={index} className='keen-slider__slide'>
                <motion.div
                  className='relative overflow-hidden rounded-3xl bg-white shadow-lg hover:shadow-xl transition-all duration-500'
                  whileHover={{ y: -8, scale: 1.02 }}
                  transition={{ type: "spring", stiffness: 300, damping: 20 }}
                >
                  {/* Image Container */}
                  <div className='relative aspect-video overflow-hidden'>
                    <motion.img
                      src={src}
                      alt={`Thumbnail Design ${index + 1}`}
                      title={`Professional YouTube Thumbnail Design ${
                        index + 1
                      } by Bilal Ahmed`}
                      className='w-full h-full object-cover transition-transform duration-500 hover:scale-110'
                      loading='lazy'
                      whileHover={{ scale: 1.05 }}
                    />

                    {/* Zoom Icon */}
                    <motion.div
                      className='absolute top-4 right-4 w-10 h-10 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center hover:bg-white/30 transition-all duration-300'
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <ZoomIn
                        className='w-5 h-5 text-white cursor-pointer'
                        onClick={(e) => {
                          e.stopPropagation();
                          setSelectedImage(src);
                        }}
                      />
                    </motion.div>
                  </div>

                  {/* Card Footer */}
                  <div className='p-6'>
                    <div className='flex items-center justify-between'>
                      <div className='flex items-center space-x-2'>
                        <motion.div
                          className='w-2 h-2 bg-green-500 rounded-full'
                          animate={{ scale: [1, 1.2, 1] }}
                          transition={{ duration: 2, repeat: Infinity }}
                        />
                        <span className='text-sm text-gray-600 font-medium'>
                          High Performance
                        </span>
                      </div>
                      <motion.div
                        whileHover={{ scale: 1.2, rotate: 15 }}
                        transition={{ duration: 0.2 }}
                      >
                        <ExternalLink className='w-4 h-4 text-gray-400' />
                      </motion.div>
                    </div>
                  </div>
                </motion.div>
              </div>
            ))}
          </div>
        </div>

        {/* Slide Indicators */}
        <div className='flex justify-center mt-8 space-x-2'>
          {thumbnails.map((_, index) => (
            <button
              key={index}
              onClick={() => instanceRef.current?.moveToIdx(index)}
              className={`w-3 h-3 rounded-full transition-all duration-300 ${
                currentSlide === index
                  ? "bg-[#FF4500] scale-125"
                  : "bg-gray-300 hover:bg-gray-400"
              }`}
            />
          ))}
        </div>

        {/* CTA */}
        <motion.div
          className='text-center mt-16'
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.8 }}
        >
          <a
            href='#contact'
            className='inline-flex items-center px-12 py-6 bg-gradient-to-r from-[#FF4500] to-[#FF6B35] text-white font-semibold text-lg rounded-2xl hover:shadow-2xl hover:scale-105 transition-all duration-300'
          >
            <span className='mr-3'>Let’s Work Together</span>
            <i className='fas fa-arrow-right'></i>
          </a>
        </motion.div>
      </div>

      {/* Image Modal */}
      <AnimatePresence>
        {selectedImage && (
          <motion.div
            className='fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4'
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={() => setSelectedImage(null)}
          >
            <motion.div
              className='relative max-w-4xl max-h-[90vh] w-full'
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.8, opacity: 0 }}
              transition={{ type: "spring", damping: 25, stiffness: 300 }}
              onClick={(e) => e.stopPropagation()}
            >
              <img
                src={selectedImage}
                alt='Enlarged thumbnail'
                title='Professional YouTube Thumbnail Design - Full Size View'
                className='w-full h-full object-contain rounded-2xl'
              />
              <button
                onClick={() => setSelectedImage(null)}
                className='absolute top-4 right-4 w-10 h-10 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center hover:bg-white/30 transition-colors'
              >
                <span className='text-white text-xl'>×</span>
              </button>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </section>
  );
}
