import { motion } from "framer-motion";
import { useInView } from "framer-motion";
import { useRef, useState, useEffect } from "react";

export default function StatsSection() {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });
  const [counters, setCounters] = useState([0, 0, 0]);

  const stats = [
    {
      number: 250,
      suffix: "+",
      title: "Thumbnails Designed",
      description: "Custom thumbnails delivered for creators across all niches",
      icon: "fas fa-image",
      color: "from-orange-500 to-red-500",
    },
    {
      number: 2,
      suffix: "+",
      title: "Years Experience",
      description: "Professional design experience in digital marketing",
      icon: "fas fa-calendar-alt",
      color: "from-blue-500 to-indigo-500",
    },
    {
      number: 98,
      suffix: "%",
      title: "Client Satisfaction",
      description: "Satisfaction rate with unlimited revisions guarantee",
      icon: "fas fa-heart",
      color: "from-green-500 to-teal-500",
    },
  ];

  // Animated counter effect
  useEffect(() => {
    if (isInView) {
      const duration = 2000; // 2 seconds
      const steps = 60;
      const stepTime = duration / steps;

      stats.forEach((stat, index) => {
        let currentStep = 0;
        const increment = stat.number / steps;

        const timer = setInterval(() => {
          currentStep++;
          const currentValue = Math.min(
            Math.floor(increment * currentStep),
            stat.number
          );

          setCounters((prev) => {
            const newCounters = [...prev];
            newCounters[index] = currentValue;
            return newCounters;
          });

          if (currentStep >= steps) {
            clearInterval(timer);
          }
        }, stepTime);
      });
    }
  }, [isInView]);

  return (
    <section
      id='stats'
      className='py-16 md:py-24 lg:py-32 px-6 lg:px-12 bg-white'
      ref={ref}
    >
      <div className='max-w-7xl mx-auto'>
        {/* Header */}
        <div className='text-center space-y-4 mb-16'>
          <p className='text-sm font-semibold text-[#FF4500] uppercase tracking-wide'>
            OUR ACHIEVEMENTS
          </p>
          <h2 className='text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 leading-tight tracking-wide'>
            PROVEN{" "}
            <span className='bg-gradient-to-r from-[#FF4500] to-[#FF6B35] bg-clip-text text-transparent'>
              RESULTS
            </span>
          </h2>
        </div>

        {/* Stats Grid */}
        <div className='grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto'>
          {stats.map((stat, index) => (
            <motion.div
              key={index}
              className='group relative bg-white rounded-2xl p-8 shadow-lg border border-gray-100 transition-all duration-500 text-center hover:bg-gradient-to-br hover:from-[#FF4500]/5 hover:to-[#FF6B35]/5'
              initial={{ opacity: 0, y: 50 }}
              animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
              transition={{ duration: 0.6, delay: index * 0.2 }}
              whileHover={{
                y: -10,
                scale: 1.03,
                boxShadow: "0 25px 50px rgba(255, 69, 0, 0.2)",
              }}
            >
              {/* Icon */}
              <div
                className={`w-16 h-16 mx-auto mb-6 rounded-2xl bg-gradient-to-r ${stat.color} flex items-center justify-center shadow-lg group-hover:scale-110 group-hover:rotate-3 transition-all duration-300`}
              >
                <i className={`${stat.icon} text-white text-2xl`} />
              </div>

              {/* Number */}
              <div className='mb-4'>
                <h3 className='text-5xl font-bold bg-gradient-to-r from-[#FF4500] to-[#FF6B35] bg-clip-text text-transparent group-hover:scale-110 transition-transform duration-300'>
                  {counters[index]}
                  {stat.suffix}
                </h3>
              </div>

              {/* Title */}
              <h4 className='text-xl font-bold text-gray-900 mb-3 group-hover:text-[#FF4500] transition-colors duration-300'>
                {stat.title}
              </h4>

              {/* Description */}
              <p className='text-gray-600 text-sm leading-relaxed'>
                {stat.description}
              </p>

              {/* Hover Effect */}
              <div className='absolute inset-0 bg-gradient-to-br from-[#FF4500]/5 to-[#FF6B35]/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-2xl' />
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
}
