import { motion } from "framer-motion";
import { useInView } from "framer-motion";
import { useRef, useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";

// Contact form schema for frontend validation
const contactSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters"),
  email: z.string().email("Please enter a valid email address"),
  subject: z.string().min(1, "Please select a subject"),
  message: z.string().min(10, "Message must be at least 10 characters"),
});

type ContactForm = z.infer<typeof contactSchema>;

export default function ContactSection() {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<ContactForm>({
    resolver: zodResolver(contactSchema),
    defaultValues: {
      name: "",
      email: "",
      subject: "",
      message: "",
    },
  });

  const onSubmit = async (data: ContactForm) => {
    setIsSubmitting(true);

    // Simulate form submission delay
    await new Promise((resolve) => setTimeout(resolve, 1000));

    // Create mailto link with form data
    const subject = encodeURIComponent(
      `${data.subject} - Contact from ${data.name}`
    );
    const body = encodeURIComponent(
      `Name: ${data.name}\nEmail: ${data.email}\nSubject: ${data.subject}\n\nMessage:\n${data.message}`
    );
    const mailtoLink = `mailto:<EMAIL>?subject=${subject}&body=${body}`;

    // Open email client
    window.open(mailtoLink, "_blank");

    // Show success message
    toast({
      title: "Ready to send!",
      description:
        "Your email client should open with the message pre-filled. You can also contact me <NAME_EMAIL>",
    });

    form.reset();
    setIsSubmitting(false);
  };

  return (
    <>
      {/* Hero Contact Section */}
      <section
        id='contact'
        className='py-20 px-6 lg:px-12 bg-gray-900'
        ref={ref}
      >
        <div className='max-w-4xl mx-auto'>
          <div className='text-center space-y-12'>
            {/* Header */}
            <motion.div
              className='space-y-8'
              initial={{ opacity: 0, y: 50 }}
              animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
              transition={{ duration: 0.8, ease: "easeOut" }}
            >
              <h2 className='text-4xl md:text-5xl lg:text-6xl font-bold text-white leading-tight'>
                Your ideas, Our Visuals,
                <br />
                More Views.
              </h2>
            </motion.div>

            {/* Contact Button */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
              transition={{ duration: 0.8, delay: 0.4 }}
            >
              <motion.a
                href='mailto:<EMAIL>'
                className='inline-block px-12 py-4 bg-gradient-to-r from-[#FF4500] to-[#FF6B35] text-white font-semibold text-lg rounded-full hover:shadow-xl transition-all duration-300 relative overflow-hidden group'
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                {/* Button shine effect */}
                <div className='absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-1000' />
                <span className='relative z-10'>Send Message</span>
              </motion.a>
            </motion.div>

            {/* Contact Info */}
            <motion.div
              className='grid grid-cols-1 md:grid-cols-2 gap-8 max-w-2xl mx-auto'
              initial={{ opacity: 0, y: 30 }}
              animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
              transition={{ duration: 0.8, delay: 0.6 }}
            >
              <div className='text-center'>
                <h4 className='font-semibold text-white mb-2'>Email:</h4>
                <p className='text-gray-300'><EMAIL></p>
              </div>
              <div className='text-center'>
                <h4 className='font-semibold text-white mb-2'>Consultation:</h4>
                <p className='text-gray-300'>+92 334 0505686</p>
              </div>
            </motion.div>
          </div>
        </div>
      </section>
    </>
  );
}
