import { motion } from "framer-motion";
import { useInView } from "framer-motion";
import { useRef } from "react";

export default function AboutSection() {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });

  const stats = [
    {
      number: "250+",
      label: "Thumbnails Designed",
      description:
        "Over 250 custom thumbnails delivered for creators in gaming, vlogging, education, and IRL categories",
    },
    {
      number: "4+",
      label: "Years Of Experience",
      description:
        "More than six years of hands-on design experience, providing reliable and high-performance visuals",
    },
    {
      number: "98%",
      label: "Client Satisfaction Rate",
      description:
        "Maintaining a 98% satisfaction score by consistently delivering fast, and results-driven designs",
    },
  ];

  return (
    <section id='about' className='py-20 px-6 lg:px-12 bg-white' ref={ref}>
      <div className='max-w-7xl mx-auto'>
        {/* Header */}
        <motion.div
          className='text-center mb-20'
          initial={{ opacity: 0, y: 50 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
        >
          <motion.div
            className='inline-block px-6 py-3 bg-[#FF4500] text-white rounded-full text-sm font-semibold mb-6'
            initial={{ scale: 0 }}
            animate={isInView ? { scale: 1 } : { scale: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            About Biviso
          </motion.div>

          <motion.h2
            className='text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 leading-tight mb-8'
            initial={{ opacity: 0, y: 30 }}
            animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
            transition={{ duration: 0.8, delay: 0.4 }}
          >
            Transforming Visions
            <br />
            into Visual Success
          </motion.h2>
        </motion.div>

        {/* Main Content */}
        <div className='grid grid-cols-1 lg:grid-cols-2 gap-16 items-center mb-20'>
          {/* Left Content */}
          <motion.div
            className='space-y-8'
            initial={{ opacity: 0, x: -50 }}
            animate={isInView ? { opacity: 1, x: 0 } : { opacity: 0, x: -50 }}
            transition={{ duration: 0.8, delay: 0.6 }}
          >
            <div className='space-y-6'>
              <p className='text-lg text-gray-600 leading-relaxed'>
                We are <strong className='text-[#FF4500]'>Biviso</strong>, a
                creative design agency that specializes in transforming content
                creators' visions into scroll-stopping visuals that drive real
                results.
              </p>

              <p className='text-lg text-gray-600 leading-relaxed'>
                Our team combines creative expertise with data-driven insights
                to create thumbnails, social media designs, and visual branding
                that not only look incredible but perform exceptionally in
                today's competitive digital landscape.
              </p>
            </div>

            <motion.a
              href='#contact'
              className='inline-block px-8 py-4 bg-[#FF4500] text-white font-semibold text-lg rounded-full hover:shadow-lg transition-all duration-300'
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              Get Your Transformation Started
            </motion.a>
          </motion.div>

          {/* Right Content - Image */}
          <motion.div
            className='relative'
            initial={{ opacity: 0, x: 50 }}
            animate={isInView ? { opacity: 1, x: 0 } : { opacity: 0, x: 50 }}
            transition={{ duration: 0.8, delay: 0.8 }}
          >
            <div className='relative rounded-3xl overflow-hidden'>
              <img
                src='/bilal-photo.png'
                alt='Bilal Ahmed - Creative Director'
                className='w-full h-auto object-cover'
              />
            </div>
          </motion.div>
        </div>

        {/* Stats Section */}
        <div className='grid grid-cols-1 md:grid-cols-3 gap-8'>
          {stats.map((stat, index) => (
            <motion.div
              key={index}
              className='text-center p-8 bg-gray-50 rounded-2xl'
              initial={{ opacity: 0, y: 30 }}
              animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
              transition={{ duration: 0.6, delay: 1.0 + index * 0.2 }}
            >
              <div className='text-4xl md:text-5xl font-bold text-[#FF4500] mb-2'>
                {stat.number}
              </div>
              <h3 className='text-xl font-bold text-gray-900 mb-4'>
                {stat.label}
              </h3>
              <p className='text-gray-600 text-sm leading-relaxed'>
                {stat.description}
              </p>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
}
