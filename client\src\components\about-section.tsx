import { motion } from "framer-motion";
import { useInView } from "framer-motion";
import { useRef } from "react";

export default function AboutSection() {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });

  return (
    <section
      id='about'
      className='py-24 px-6 lg:px-12 bg-gradient-to-br from-gray-50 via-white to-gray-50'
      ref={ref}
    >
      <div className='max-w-7xl mx-auto'>
        <div className='grid lg:grid-cols-2 gap-20 items-center'>
          {/* Left Content */}
          <motion.div
            className='space-y-10'
            initial={{ opacity: 0, x: -50 }}
            animate={isInView ? { opacity: 1, x: 0 } : { opacity: 0, x: -50 }}
            transition={{ duration: 0.8, ease: "easeOut" }}
          >
            {/* Enhanced Badge */}
            <motion.div
              className='inline-flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-[#FF4500] to-[#FF6B35] text-white rounded-full text-sm font-semibold shadow-lg'
              initial={{ scale: 0, rotate: -10 }}
              animate={
                isInView ? { scale: 1, rotate: 0 } : { scale: 0, rotate: -10 }
              }
              transition={{ duration: 0.6, delay: 0.2, type: "spring" }}
            >
              <i className='uil uil-users-alt text-lg'></i>
              About Our Agency
            </motion.div>

            {/* Enhanced Heading */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
              transition={{ duration: 0.8, delay: 0.4 }}
            >
              <h2 className='text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 leading-tight text-left'>
                Transforming Visions into{" "}
                <span className='bg-gradient-to-r from-[#FF4500] to-[#FF6B35] bg-clip-text text-transparent'>
                  Visual Success
                </span>
              </h2>
            </motion.div>

            {/* Enhanced Description */}
            <motion.div
              className='space-y-6'
              initial={{ opacity: 0, y: 30 }}
              animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
              transition={{ duration: 0.8, delay: 0.6 }}
            >
              <div className='relative'>
                <div className='absolute -left-4 top-0 w-1 h-full bg-gradient-to-b from-[#FF4500] to-[#FF6B35] rounded-full'></div>
                <p className='text-lg text-gray-700 leading-relaxed pl-8 font-medium'>
                  We are{" "}
                  <strong className='text-[#FF4500] font-bold'>Biviso</strong>,
                  a creative design agency that specializes in transforming
                  content creators' visions into scroll-stopping visuals that
                  drive real results.
                </p>
              </div>

              <p className='text-lg text-gray-600 leading-relaxed'>
                Our team combines creative expertise with data-driven insights
                to create thumbnails, social media designs, and visual branding
                that not only look incredible but perform exceptionally in
                today's competitive digital landscape.
              </p>
            </motion.div>

            {/* Enhanced Stats */}
            <motion.div
              className='grid grid-cols-2 gap-6'
              initial={{ opacity: 0, y: 30 }}
              animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
              transition={{ duration: 0.8, delay: 0.8 }}
            >
              <motion.div
                className='relative p-6 bg-white rounded-2xl shadow-lg border border-gray-100 text-center lg:text-left'
                whileHover={{ scale: 1.05, y: -5 }}
                transition={{ duration: 0.3 }}
              >
                <div className='absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-[#FF4500] to-[#FF6B35] rounded-t-2xl'></div>
                <div className='text-4xl font-bold text-[#FF4500] mb-2'>
                  250+
                </div>
                <div className='text-gray-600 font-medium'>
                  Thumbnails Created
                </div>
              </motion.div>

              <motion.div
                className='relative p-6 bg-white rounded-2xl shadow-lg border border-gray-100 text-center lg:text-left'
                whileHover={{ scale: 1.05, y: -5 }}
                transition={{ duration: 0.3 }}
              >
                <div className='absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-[#FF4500] to-[#FF6B35] rounded-t-2xl'></div>
                <div className='text-4xl font-bold text-[#FF4500] mb-2'>2+</div>
                <div className='text-gray-600 font-medium'>
                  Years Experience
                </div>
              </motion.div>
            </motion.div>

            {/* Enhanced CTA Button */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
              transition={{ duration: 0.8, delay: 1.0 }}
            >
              <motion.a
                href='#contact'
                className='inline-flex items-center gap-3 px-8 py-4 bg-gradient-to-r from-[#FF4500] to-[#FF6B35] text-white font-semibold rounded-full shadow-xl hover:shadow-2xl transition-all duration-300 group'
                whileHover={{ scale: 1.05, y: -2 }}
                whileTap={{ scale: 0.95 }}
              >
                <span>Get Your Transformation Started</span>
                <motion.i
                  className='uil uil-arrow-right text-lg'
                  animate={{ x: [0, 5, 0] }}
                  transition={{ duration: 1.5, repeat: Infinity }}
                ></motion.i>
              </motion.a>
            </motion.div>
          </motion.div>

          {/* Right Content - Cool Avatars */}
          <motion.div
            className='relative'
            initial={{ opacity: 0, x: 50 }}
            animate={isInView ? { opacity: 1, x: 0 } : { opacity: 0, x: 50 }}
            transition={{ duration: 0.8, delay: 0.8 }}
          >
            <div className='relative'>
              {/* Main Avatar */}
              <motion.div
                className='relative z-10 mx-auto w-80 h-80 rounded-full bg-gradient-to-br from-[#FF4500] to-[#FF6B35] p-2 shadow-2xl'
                animate={{
                  rotate: [0, 5, -5, 0],
                  scale: [1, 1.02, 1],
                }}
                transition={{
                  duration: 6,
                  repeat: Infinity,
                  ease: "easeInOut",
                }}
              >
                <div className='w-full h-full rounded-full bg-white p-4 flex items-center justify-center'>
                  <motion.div
                    className='w-full h-full rounded-full bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center text-8xl'
                    whileHover={{ scale: 1.1 }}
                    transition={{ duration: 0.3 }}
                  >
                    <i className='uil uil-user text-[#FF4500]'></i>
                  </motion.div>
                </div>
              </motion.div>

              {/* Floating Elements */}
              <motion.div
                className='absolute top-10 -left-10 w-20 h-20 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full flex items-center justify-center shadow-lg'
                animate={{
                  y: [0, -20, 0],
                  rotate: [0, 10, 0],
                }}
                transition={{
                  duration: 4,
                  repeat: Infinity,
                  ease: "easeInOut",
                }}
              >
                <i className='uil uil-palette text-white text-2xl'></i>
              </motion.div>

              <motion.div
                className='absolute top-20 -right-8 w-16 h-16 bg-gradient-to-br from-green-400 to-green-600 rounded-full flex items-center justify-center shadow-lg'
                animate={{
                  y: [0, 15, 0],
                  rotate: [0, -15, 0],
                }}
                transition={{
                  duration: 5,
                  repeat: Infinity,
                  ease: "easeInOut",
                  delay: 1,
                }}
              >
                <i className='uil uil-chart-line text-white text-xl'></i>
              </motion.div>

              <motion.div
                className='absolute bottom-16 -left-6 w-14 h-14 bg-gradient-to-br from-purple-400 to-purple-600 rounded-full flex items-center justify-center shadow-lg'
                animate={{
                  y: [0, -10, 0],
                  rotate: [0, 20, 0],
                }}
                transition={{
                  duration: 3.5,
                  repeat: Infinity,
                  ease: "easeInOut",
                  delay: 2,
                }}
              >
                <i className='uil uil-bullseye text-white text-lg'></i>
              </motion.div>

              <motion.div
                className='absolute bottom-8 -right-4 w-18 h-18 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full flex items-center justify-center shadow-lg'
                animate={{
                  y: [0, 12, 0],
                  rotate: [0, -10, 0],
                }}
                transition={{
                  duration: 4.5,
                  repeat: Infinity,
                  ease: "easeInOut",
                  delay: 0.5,
                }}
              >
                <i className='uil uil-dollar-sign text-white text-xl'></i>
              </motion.div>

              {/* Background Glow */}
              <div className='absolute inset-0 bg-gradient-to-br from-[#FF4500]/20 to-[#FF6B35]/20 rounded-full blur-3xl -z-10'></div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
