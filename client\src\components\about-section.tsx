import { motion } from "framer-motion";
import { useInView } from "framer-motion";
import { useRef } from "react";

export default function AboutSection() {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });

  return (
    <section
      id='about'
      className='py-16 md:py-24 lg:py-32 px-6 lg:px-12 bg-white'
      ref={ref}
    >
      <div className='max-w-7xl mx-auto'>
        {/* Header */}
        <div className='text-center space-y-4 mb-16'>
          <p className='text-sm font-semibold text-[#FF4500] uppercase tracking-wide'>
            ABOUT BIVISO
          </p>
          <h2 className='text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 leading-tight tracking-wide'>
            MEET THE{" "}
            <span className='bg-gradient-to-r from-[#FF4500] to-[#FF6B35] bg-clip-text text-transparent'>
              CREATIVE AGENCY
            </span>
          </h2>
        </div>

        {/* Main Content */}
        <div className='grid grid-cols-1 lg:grid-cols-2 gap-16 items-center'>
          {/* Left Content */}
          <motion.div
            className='space-y-8'
            initial={{ opacity: 0, x: -50 }}
            animate={isInView ? { opacity: 1, x: 0 } : { opacity: 0, x: -50 }}
            transition={{ duration: 0.8, delay: 0.4 }}
          >
            <div className='space-y-6'>
              <h3 className='text-2xl font-bold text-gray-900'>
                Transforming Visions into Visual Success
              </h3>

              <p className='text-lg text-gray-600 leading-relaxed'>
                We are <strong className='text-[#FF4500]'>Biviso</strong>, a
                creative design agency that specializes in transforming content
                creators' visions into scroll-stopping visuals that drive real
                results.
              </p>

              <p className='text-lg text-gray-600 leading-relaxed'>
                Our team combines creative expertise with data-driven insights
                to create thumbnails, social media designs, and visual branding
                that not only look incredible but perform exceptionally in
                today's competitive digital landscape.
              </p>
            </div>

            <div className='flex flex-col sm:flex-row gap-4'>
              <a
                href='#contact'
                className='px-6 py-3 bg-gradient-to-r from-[#FF4500] to-[#FF6B35] text-white font-semibold text-sm rounded-full hover:shadow-lg transition-all duration-300'
              >
                START YOUR PROJECT
              </a>
              <a
                href='#portfolio'
                className='px-6 py-3 border-2 border-[#FF4500] text-[#FF4500] font-semibold text-sm rounded-full hover:bg-[#FF4500] hover:text-white transition-all duration-300'
              >
                VIEW OUR WORK
              </a>
            </div>
          </motion.div>

          {/* Right Content - Visual */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            animate={isInView ? { opacity: 1, x: 0 } : { opacity: 0, x: 50 }}
            transition={{ duration: 0.8, delay: 0.6 }}
          >
            <div className='relative'>
              {/* Team Avatars Grid */}
              <div className='grid grid-cols-2 gap-6'>
                {/* Avatar 1 */}
                <motion.div
                  className='bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl p-6 text-center'
                  whileHover={{ scale: 1.05, rotate: 2 }}
                  transition={{ type: "spring", stiffness: 300 }}
                >
                  <div className='w-16 h-16 mx-auto mb-4 bg-white rounded-full flex items-center justify-center'>
                    <i className='fas fa-user-tie text-blue-500 text-2xl' />
                  </div>
                  <h4 className='text-white font-bold text-sm'>
                    Creative Director
                  </h4>
                  <p className='text-blue-100 text-xs mt-1'>Design Strategy</p>
                </motion.div>

                {/* Avatar 2 */}
                <motion.div
                  className='bg-gradient-to-br from-green-500 to-teal-600 rounded-2xl p-6 text-center'
                  whileHover={{ scale: 1.05, rotate: -2 }}
                  transition={{ type: "spring", stiffness: 300 }}
                >
                  <div className='w-16 h-16 mx-auto mb-4 bg-white rounded-full flex items-center justify-center'>
                    <i className='fas fa-paint-brush text-green-500 text-2xl' />
                  </div>
                  <h4 className='text-white font-bold text-sm'>
                    Lead Designer
                  </h4>
                  <p className='text-green-100 text-xs mt-1'>Visual Creation</p>
                </motion.div>

                {/* Avatar 3 */}
                <motion.div
                  className='bg-gradient-to-br from-orange-500 to-red-600 rounded-2xl p-6 text-center'
                  whileHover={{ scale: 1.05, rotate: 2 }}
                  transition={{ type: "spring", stiffness: 300 }}
                >
                  <div className='w-16 h-16 mx-auto mb-4 bg-white rounded-full flex items-center justify-center'>
                    <i className='fas fa-chart-line text-orange-500 text-2xl' />
                  </div>
                  <h4 className='text-white font-bold text-sm'>Data Analyst</h4>
                  <p className='text-orange-100 text-xs mt-1'>
                    Performance Tracking
                  </p>
                </motion.div>

                {/* Avatar 4 */}
                <motion.div
                  className='bg-gradient-to-br from-purple-500 to-pink-600 rounded-2xl p-6 text-center'
                  whileHover={{ scale: 1.05, rotate: -2 }}
                  transition={{ type: "spring", stiffness: 300 }}
                >
                  <div className='w-16 h-16 mx-auto mb-4 bg-white rounded-full flex items-center justify-center'>
                    <i className='fas fa-users text-purple-500 text-2xl' />
                  </div>
                  <h4 className='text-white font-bold text-sm'>
                    Client Success
                  </h4>
                  <p className='text-purple-100 text-xs mt-1'>
                    Project Management
                  </p>
                </motion.div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
