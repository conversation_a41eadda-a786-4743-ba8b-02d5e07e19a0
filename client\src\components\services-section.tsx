import { motion } from "framer-motion";
import { useInView } from "framer-motion";
import { useRef } from "react";

export default function ServicesSection() {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });

  const services = [
    {
      title: "YouTube Thumbnails",
      description:
        "Scroll-stopping designs crafted to boost your CTR. We create high-performing YouTube thumbnails that grab attention, spark curiosity, and drive more views. Designed with your niche, audience, and growth goals in mind.",
      bgColor: "bg-[#FF4500]",
      link: "#contact",
    },
    {
      title: "Social Media Design",
      description:
        "Bold, branded visuals that get noticed. From Instagram posts to Twitter banners, we design eye-catching content that aligns with your brand and engages your followers. Perfect for creators, influencers, and brands.",
      bgColor: "bg-[#4285F4]",
      link: "#contact",
    },
    {
      title: "Design Consultation",
      description:
        "Expert guidance to elevate your visual presence. Not sure what works best for your channel or brand? We offer personalized design consultations to help refine your aesthetic, improve engagement, and build a consistent visual strategy.",
      bgColor: "bg-[#9C27B0]",
      link: "#contact",
    },
  ];

  return (
    <section id='services' className='py-20 px-6 lg:px-12 bg-white' ref={ref}>
      <div className='max-w-7xl mx-auto'>
        {/* Header */}
        <motion.div
          className='text-center mb-20'
          initial={{ opacity: 0, y: 50 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
        >
          <h2 className='text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 leading-tight mb-4'>
            What We Deliver
          </h2>
        </motion.div>

        {/* Services Grid */}
        <div className='grid grid-cols-1 md:grid-cols-3 gap-12 max-w-6xl mx-auto'>
          {services.map((service, index) => (
            <motion.div
              key={index}
              className='group text-left'
              initial={{ opacity: 0, y: 30 }}
              animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
              transition={{ duration: 0.5, delay: index * 0.2 }}
            >
              {/* Icon */}
              <motion.div
                className={`w-16 h-16 mb-8 rounded-2xl ${service.bgColor} flex items-center justify-center`}
                whileHover={{ scale: 1.05 }}
                transition={{ type: "spring", stiffness: 300, damping: 20 }}
              ></motion.div>

              {/* Content */}
              <h3 className='text-2xl font-bold text-gray-900 mb-6'>
                {service.title}
              </h3>
              <p className='text-gray-600 leading-relaxed mb-8 text-base'>
                {service.description}
              </p>

              {/* CTA Link */}
              <motion.a
                href={service.link}
                className='inline-flex items-center text-gray-900 font-semibold hover:text-[#FF4500] transition-colors duration-300'
                whileHover={{ x: 5 }}
              >
                See Plans & Pricing
                <svg
                  className='w-4 h-4 ml-2'
                  fill='none'
                  stroke='currentColor'
                  viewBox='0 0 24 24'
                >
                  <path
                    strokeLinecap='round'
                    strokeLinejoin='round'
                    strokeWidth={2}
                    d='M9 5l7 7-7 7'
                  />
                </svg>
              </motion.a>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
}
