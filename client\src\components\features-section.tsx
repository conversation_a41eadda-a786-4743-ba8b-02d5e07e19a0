import { motion } from "framer-motion";
import { useInView } from "framer-motion";
import { useRef } from "react";

export default function FeaturesSection() {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });

  const features = [
    {
      title: "CTR-Focused Visual Strategy",
      description:
        "We design YouTube thumbnails and social content that are engineered for maximum click-through rates, helping your videos outperform the algorithm.",
      bgColor: "bg-gradient-to-br from-[#4285F4] to-[#1976D2]",
      icon: "uil uil-lightbulb-alt",
    },
    {
      title: "End-to-End Creative Services",
      description:
        "From thumbnails to brand consultation, we're your all-in-one partner for consistent, high-performing visuals across your channel and social media platforms.",
      bgColor: "bg-gradient-to-br from-[#9C27B0] to-[#7B1FA2]",
      icon: "uil uil-lock",
    },
    {
      title: "Niche-Based Design Approach",
      description:
        "We tailor every design to your content type, target audience, and YouTube niche — ensuring each thumbnail tells the right story at the right moment.",
      bgColor: "bg-gradient-to-br from-[#4CAF50] to-[#388E3C]",
      icon: "uil uil-check-circle",
    },
    {
      title: "Creator-Friendly Pricing",
      description:
        "Professional design shouldn't be out of reach. Our pricing is transparent, competitive, and flexible — built for creators at every stage.",
      bgColor: "bg-gradient-to-br from-[#FF4500] to-[#FF6B35]",
      icon: "uil uil-handshake",
    },
  ];

  return (
    <section id='features' className='py-20 px-6 lg:px-12 bg-white' ref={ref}>
      <div className='max-w-7xl mx-auto'>
        {/* Header */}
        <motion.div
          className='text-center mb-20'
          initial={{ opacity: 0, y: 50 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
        >
          <motion.div
            className='inline-block px-6 py-3 bg-[#FF4500] text-white rounded-full text-sm font-semibold mb-6'
            initial={{ scale: 0 }}
            animate={isInView ? { scale: 1 } : { scale: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            Why Choose Us
          </motion.div>

          <motion.h2
            className='text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 leading-tight'
            initial={{ opacity: 0, y: 30 }}
            animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
            transition={{ duration: 0.8, delay: 0.4 }}
          >
            We Build Ideas Driven By The Future
          </motion.h2>
        </motion.div>

        {/* Features Grid */}
        <div className='grid grid-cols-1 md:grid-cols-2 gap-8 max-w-6xl mx-auto'>
          {features.map((feature, index) => (
            <motion.div
              key={index}
              className='group bg-gray-50 rounded-3xl p-8 hover:bg-white hover:shadow-xl transition-all duration-500 border border-gray-100 text-left relative'
              initial={{ opacity: 0, y: 50 }}
              animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
              transition={{ duration: 0.6, delay: index * 0.2 }}
              whileHover={{
                y: -8,
                scale: 1.02,
                boxShadow: "0 25px 50px rgba(0, 0, 0, 0.1)",
              }}
            >
              {/* Icon */}
              <motion.div
                className={`w-16 h-16 mb-6 rounded-2xl ${feature.bgColor} flex items-center justify-center shadow-lg`}
                whileHover={{ scale: 1.1, rotate: 5 }}
                transition={{ type: "spring", stiffness: 300, damping: 20 }}
              >
                <i className={`${feature.icon} text-white text-2xl`} />
              </motion.div>

              {/* Content */}
              <h3 className='text-xl font-bold text-gray-900 mb-4 group-hover:text-[#FF4500] transition-colors duration-300'>
                {feature.title}
              </h3>
              <p className='text-gray-600 leading-relaxed text-sm'>
                {feature.description}
              </p>

              {/* Hover Effect Background */}
              <div className='absolute inset-0 bg-gradient-to-br from-[#FF4500]/5 to-[#FF6B35]/5 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none' />
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
}
