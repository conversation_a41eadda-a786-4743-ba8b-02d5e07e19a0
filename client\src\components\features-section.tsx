import { motion } from "framer-motion";
import { useInView } from "framer-motion";
import { useRef } from "react";

export default function FeaturesSection() {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });

  const features = [
    {
      title: "End-to-End Solutions",
      description:
        "We are your one-stop shop for all your digital presence needs, providing solutions to help your business stand out online",
      bgColor: "bg-[#4285F4]",
    },
    {
      title: "Ongoing Maintenance & Support",
      description:
        "Updates, patches, fixes, and round-the-clock support to keep your systems running smoothly and efficiently",
      bgColor: "bg-[#9C27B0]",
    },
    {
      title: "Affordable Pricing",
      description:
        "Our services are reasonably priced and are of high quality, and customized to suit your requirements",
      bgColor: "bg-[#4CAF50]",
    },
    {
      title: "Fast Turnaround Times",
      description:
        "Efficient management to ensure every project is delivered on time, meeting deadlines without compromising on quality",
      bgColor: "bg-[#FF4500]",
    },
  ];

  return (
    <section id='features' className='py-20 px-6 lg:px-12 bg-white' ref={ref}>
      <div className='max-w-7xl mx-auto'>
        {/* Header */}
        <motion.div
          className='text-center mb-20'
          initial={{ opacity: 0, y: 50 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
        >
          <motion.div
            className='inline-block px-6 py-3 bg-[#FF4500] text-white rounded-full text-sm font-semibold mb-6'
            initial={{ scale: 0 }}
            animate={isInView ? { scale: 1 } : { scale: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            Why Choose Us
          </motion.div>

          <motion.h2
            className='text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 leading-tight'
            initial={{ opacity: 0, y: 30 }}
            animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
            transition={{ duration: 0.8, delay: 0.4 }}
          >
            We Build Ideas Driven By The Future
          </motion.h2>
        </motion.div>

        {/* Features Grid */}
        <div className='grid grid-cols-1 md:grid-cols-2 gap-12 max-w-6xl mx-auto'>
          {features.map((feature, index) => (
            <motion.div
              key={index}
              className='group text-left'
              initial={{ opacity: 0, y: 50 }}
              animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
              transition={{ duration: 0.6, delay: index * 0.2 }}
            >
              {/* Icon */}
              <motion.div
                className={`w-16 h-16 mb-8 rounded-2xl ${feature.bgColor} flex items-center justify-center`}
                whileHover={{ scale: 1.05 }}
                transition={{ type: "spring", stiffness: 300, damping: 20 }}
              ></motion.div>

              {/* Content */}
              <h3 className='text-2xl font-bold text-gray-900 mb-6'>
                {feature.title}
              </h3>
              <p className='text-gray-600 leading-relaxed text-base'>
                {feature.description}
              </p>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
}
