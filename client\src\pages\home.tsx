import { useEffect, lazy, Suspense } from "react";
import Navigation from "@/components/navigation";
import HeroSection from "@/components/hero-section";
import Loading from "@/components/loading";

// Lazy load components that are below the fold
const AboutSection = lazy(() => import("@/components/about-section"));
const OffersSection = lazy(() => import("@/components/offers-section"));

const ServicesSection = lazy(() => import("@/components/services-section"));
const PortfolioSection = lazy(() => import("@/components/portfolio-section"));
const FeaturesSection = lazy(() => import("@/components/features-section"));
const TestimonialsSection = lazy(
  () => import("@/components/testimonials-section")
);
const StatsSection = lazy(() => import("@/components/stats-section"));

const ContactSection = lazy(() => import("@/components/contact-section"));
const Footer = lazy(() => import("@/components/footer"));

export default function Home() {
  useEffect(() => {
    // Enhanced smooth scrolling for navigation links
    const handleLinkClick = (e: Event) => {
      const target = e.target as HTMLAnchorElement;
      if (target.getAttribute("href")?.startsWith("#")) {
        e.preventDefault();
        const targetId = target.getAttribute("href")!.substring(1);
        const element = document.getElementById(targetId);
        if (element) {
          const headerOffset = 80; // Account for fixed header
          const elementPosition = element.getBoundingClientRect().top;
          const offsetPosition =
            elementPosition + window.pageYOffset - headerOffset;

          window.scrollTo({
            top: offsetPosition,
            behavior: "smooth",
          });
        }
      }
    };

    // Add scroll-based animations
    const observerOptions = {
      threshold: 0.1,
      rootMargin: "0px 0px -50px 0px",
    };

    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add("animate");
        }
      });
    }, observerOptions);

    // Observe all fade-in-up elements
    const fadeElements = document.querySelectorAll(".fade-in-up");
    fadeElements.forEach((el) => observer.observe(el));

    document.addEventListener("click", handleLinkClick);

    return () => {
      document.removeEventListener("click", handleLinkClick);
      observer.disconnect();
    };
  }, []);

  return (
    <div className='min-h-screen bg-white text-gray-900'>
      <Navigation />
      <HeroSection />
      <Suspense fallback={<Loading message='Loading services...' />}>
        <ServicesSection />
      </Suspense>
      <Suspense fallback={<Loading message='Loading portfolio...' />}>
        <PortfolioSection />
      </Suspense>
      <Suspense fallback={<Loading message='Loading features...' />}>
        <FeaturesSection />
      </Suspense>
      <Suspense fallback={<Loading message='Loading about...' />}>
        <AboutSection />
      </Suspense>
      <Suspense fallback={<Loading message='Loading testimonials...' />}>
        <TestimonialsSection />
      </Suspense>

      <Suspense fallback={<Loading message='Loading contact...' />}>
        <ContactSection />
      </Suspense>
      <Suspense fallback={<Loading message='Loading footer...' />}>
        <Footer />
      </Suspense>
    </div>
  );
}
